
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';
import { MapContainer, TileLayer, <PERSON>er, Polyline, Popup, CircleMarker } from 'react-leaflet';
import { AlertCircle, Activity, TrendingUp, Train, Clock, Zap, CheckCircle, AlertTriangle, Cpu, Eye } from 'lucide-react';
import 'leaflet/dist/leaflet.css';

const API_BASE = 'http://localhost:8000';

const Dashboard = () => {
  const [networkState, setNetworkState] = useState(null);
  const [aiAnalysis, setAiAnalysis] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const [trains, setTrains] = useState([]);
  const [events, setEvents] = useState([]);
  const [selectedTrain, setSelectedTrain] = useState(null);
  const [simulationRunning, setSimulationRunning] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [demoStats, setDemoStats] = useState(null);

  // Fetch data on component mount
  useEffect(() => {
    fetchInitialData();
    const interval = setInterval(fetchRealtimeData, 2000);
    return () => clearInterval(interval);
  }, []);

  const fetchInitialData = async () => {
    try {
      const [stateRes, analysisRes, metricsRes, trainsRes, statsRes] = await Promise.all([
        axios.get(`${API_BASE}/api/network/state`),
        axios.get(`${API_BASE}/api/ai/analysis`),
        axios.get(`${API_BASE}/api/metrics`),
        axios.get(`${API_BASE}/api/trains`),
        axios.get(`${API_BASE}/api/demo/statistics`)
      ]);
      
      setNetworkState(stateRes.data);
      setAiAnalysis(analysisRes.data);
      setMetrics(metricsRes.data);
      setTrains(trainsRes.data.trains);
      setDemoStats(statsRes.data);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const fetchRealtimeData = async () => {
    if (!simulationRunning) return;
    
    try {
      const [stateRes, eventsRes] = await Promise.all([
        axios.get(`${API_BASE}/api/network/state`),
        axios.get(`${API_BASE}/api/events/recent`)
      ]);
      
      setNetworkState(stateRes.data);
      setEvents(eventsRes.data.events || []);
    } catch (error) {
      console.error('Error fetching realtime data:', error);
    }
  };

  const startSimulation = async () => {
    try {
      await axios.post(`${API_BASE}/api/simulation/control`, { action: 'start' });
      setSimulationRunning(true);
    } catch (error) {
      console.error('Error starting simulation:', error);
    }
  };

  const stopSimulation = async () => {
    try {
      await axios.post(`${API_BASE}/api/simulation/control`, { action: 'stop' });
      setSimulationRunning(false);
    } catch (error) {
      console.error('Error stopping simulation:', error);
    }
  };

  // Sample data for charts
  const delayTrendData = [
    { time: '00:00', withAI: 5, withoutAI: 12 },
    { time: '04:00', withAI: 4, withoutAI: 15 },
    { time: '08:00', withAI: 7, withoutAI: 22 },
    { time: '12:00', withAI: 6, withoutAI: 18 },
    { time: '16:00', withAI: 8, withoutAI: 25 },
    { time: '20:00', withAI: 5, withoutAI: 14 },
  ];

  const performanceData = [
    { metric: 'On-Time', value: 95, fullMark: 100 },
    { metric: 'Efficiency', value: 89, fullMark: 100 },
    { metric: 'Safety', value: 98, fullMark: 100 },
    { metric: 'Utilization', value: 85, fullMark: 100 },
    { metric: 'Energy', value: 92, fullMark: 100 },
  ];

  const priorityDistribution = [
    { name: 'Express', value: 45, color: '#3b82f6' },
    { name: 'Local', value: 30, color: '#10b981' },
    { name: 'Freight', value: 25, color: '#f59e0b' },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
              RAILS-AI Control Center
            </h1>
            <p className="text-gray-400 mt-2">Real-time Adaptive Intelligence for Line Section Optimization</p>
          </div>
          <div className="flex gap-4">
            <button
              onClick={simulationRunning ? stopSimulation : startSimulation}
              className={`px-6 py-3 rounded-lg font-semibold transition-all ${
                simulationRunning 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-green-600 hover:bg-green-700'
              }`}
            >
              {simulationRunning ? 'Stop Simulation' : 'Start Simulation'}
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex gap-2 mb-6">
        {['overview', 'ai-insights', 'network', 'performance'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-lg capitalize transition-all ${
              activeTab === tab 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
            }`}
          >
            {tab.replace('-', ' ')}
          </button>
        ))}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-12 gap-4">
        {/* Left Panel - Key Metrics */}
        <div className="col-span-3 space-y-4">
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-400" />
              System Status
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">AI Status</span>
                <span className="flex items-center gap-1 text-green-400">
                  <CheckCircle className="w-4 h-4" /> Active
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Network Load</span>
                <span className="text-yellow-400">67%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Active Trains</span>
                <span className="text-white font-semibold">{trains.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Decisions/min</span>
                <span className="text-purple-400">247</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-400" />
              Performance Gains
            </h3>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-400">Delay Reduction</span>
                  <span className="text-sm text-green-400">-64.4%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div className="bg-green-400 h-2 rounded-full" style={{width: '64%'}}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-400">Throughput</span>
                  <span className="text-sm text-blue-400">+31.5%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div className="bg-blue-400 h-2 rounded-full" style={{width: '31%'}}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-400">Energy Saved</span>
                  <span className="text-sm text-yellow-400">-18.2%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div className="bg-yellow-400 h-2 rounded-full" style={{width: '18%'}}></div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-400" />
              Active Alerts
            </h3>
            <div className="space-y-2">
              <div className="bg-yellow-900/20 border border-yellow-600/50 rounded p-2">
                <div className="text-sm font-semibold text-yellow-400">High Congestion</div>
                <div className="text-xs text-gray-400">Kanpur-Allahabad section</div>
              </div>
              <div className="bg-blue-900/20 border border-blue-600/50 rounded p-2">
                <div className="text-sm font-semibold text-blue-400">Platform Change</div>
                <div className="text-xs text-gray-400">Train T1003 at Aligarh</div>
              </div>
            </div>
          </div>
        </div>

        {/* Center Panel - Main Visualization */}
        <div className="col-span-6 space-y-4">
          {activeTab === 'overview' && (
            <>
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Network Visualization</h3>
                <div className="bg-gray-900 rounded-lg p-4 h-96 flex items-center justify-center">
                  <div className="relative w-full h-full">
                    {/* Simplified Network Visualization */}
                    <svg className="w-full h-full" viewBox="0 0 800 400">
                      {/* Track Lines */}
                      <line x1="100" y1="200" x2="700" y2="200" stroke="#4b5563" strokeWidth="2" />
                      <line x1="250" y1="200" x2="350" y2="150" stroke="#4b5563" strokeWidth="2" />
                      <line x1="350" y1="150" x2="450" y2="200" stroke="#4b5563" strokeWidth="2" />
                      
                      {/* Stations */}
                      {[100, 250, 400, 550, 700].map((x, i) => (
                        <g key={i}>
                          <circle cx={x} cy="200" r="12" fill="#1f2937" stroke="#3b82f6" strokeWidth="2" />
                          <text x={x} y="230" textAnchor="middle" fill="#9ca3af" fontSize="12">
                            Station {i + 1}
                          </text>
                        </g>
                      ))}
                      
                      {/* Trains */}
                      <rect x="180" y="190" width="30" height="20" fill="#10b981" rx="2" />
                      <rect x="320" y="140" width="30" height="20" fill="#f59e0b" rx="2" />
                      <rect x="480" y="190" width="30" height="20" fill="#3b82f6" rx="2" />
                      
                      {/* Status Indicators */}
                      <circle cx="750" cy="50" r="5" fill="#10b981" />
                      <text x="760" y="55" fill="#9ca3af" fontSize="12">On Time</text>
                      
                      <circle cx="750" cy="70" r="5" fill="#f59e0b" />
                      <text x="760" y="75" fill="#9ca3af" fontSize="12">Minor Delay</text>
                      
                      <circle cx="750" cy="90" r="5" fill="#ef4444" />
                      <text x="760" y="95" fill="#9ca3af" fontSize="12">Major Delay</text>
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Delay Comparison (With AI vs Without AI)</h3>
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={delayTrendData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="time" stroke="#9ca3af" />
                    <YAxis stroke="#9ca3af" />
                    <Tooltip contentStyle={{ backgroundColor: '#1f2937', border: 'none' }} />
                    <Legend />
                    <Line type="monotone" dataKey="withAI" stroke="#10b981" strokeWidth={2} name="With RAILS-AI" />
                    <Line type="monotone" dataKey="withoutAI" stroke="#ef4444" strokeWidth={2} name="Without AI" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeTab === 'ai-insights' && (
            <>
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Cpu className="w-5 h-5 text-purple-400" />
                  AI Decision Engine
                </h3>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="bg-gray-900 rounded-lg p-3">
                    <div className="text-sm text-gray-400">Model Confidence</div>
                    <div className="text-2xl font-bold text-purple-400">94.7%</div>
                  </div>
                  <div className="bg-gray-900 rounded-lg p-3">
                    <div className="text-sm text-gray-400">Learning Rate</div>
                    <div className="text-2xl font-bold text-green-400">Improving</div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 rounded-lg p-3 border border-blue-600/30">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-semibold text-blue-400">Latest Decision</span>
                      <span className="text-xs text-gray-400">2 seconds ago</span>
                    </div>
                    <div className="text-sm text-gray-300">
                      Prioritized Express T1001 over Freight T1004 at Kanpur crossing
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Reason: Higher priority + passenger service
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-r from-green-900/50 to-blue-900/50 rounded-lg p-3 border border-green-600/30">
                    <div className="text-sm font-semibold text-green-400 mb-2">Optimization Achieved</div>
                    <div className="text-sm text-gray-300">
                      Reduced cascading delays by 35% through predictive rescheduling
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Performance Radar</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={performanceData}>
                    <PolarGrid stroke="#374151" />
                    <PolarAngleAxis dataKey="metric" stroke="#9ca3af" />
                    <PolarRadiusAxis stroke="#9ca3af" />
                    <Radar name="RAILS-AI" dataKey="value" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeTab === 'network' && (
            <div className="bg-gray-800 rounded-lg p-4 h-full">
              <h3 className="text-lg font-semibold mb-3">Live Train Positions</h3>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {trains.map((train) => (
                  <div key={train.id} className="bg-gray-900 rounded-lg p-3 hover:bg-gray-700 transition-colors cursor-pointer">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-3">
                        <Train className={`w-5 h-5 ${
                          train.type === 'express' ? 'text-blue-400' :
                          train.type === 'local' ? 'text-green-400' : 'text-yellow-400'
                        }`} />
                        <div>
                          <div className="font-semibold">{train.id}</div>
                          <div className="text-sm text-gray-400">{train.type} • Priority {train.priority}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm">{train.position}</div>
                        <div className={`text-xs ${
                          train.delays > 10 ? 'text-red-400' :
                          train.delays > 5 ? 'text-yellow-400' : 'text-green-400'
                        }`}>
                          {train.delays > 0 ? `${train.delays} min delay` : 'On time'}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <>
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Train Type Distribution</h3>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={priorityDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({name, percent}) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {priorityDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip contentStyle={{ backgroundColor: '#1f2937', border: 'none' }} />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">Hourly Throughput</h3>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={[
                    {hour: '00:00', trains: 12},
                    {hour: '04:00', trains: 8},
                    {hour: '08:00', trains: 22},
                    {hour: '12:00', trains: 18},
                    {hour: '16:00', trains: 25},
                    {hour: '20:00', trains: 15},
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="hour" stroke="#9ca3af" />
                    <YAxis stroke="#9ca3af" />
                    <Tooltip contentStyle={{ backgroundColor: '#1f2937', border: 'none' }} />
                    <Bar dataKey="trains" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </>
          )}
        </div>

        {/* Right Panel - Event Log & Controls */}
        <div className="col-span-3 space-y-4">
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Eye className="w-5 h-5 text-blue-400" />
              What-If Analysis
            </h3>
            <div className="space-y-3">
              <button className="w-full bg-blue-600 hover:bg-blue-700 rounded-lg py-2 px-3 text-sm font-semibold transition-colors">
                Simulate Signal Failure
              </button>
              <button className="w-full bg-purple-600 hover:bg-purple-700 rounded-lg py-2 px-3 text-sm font-semibold transition-colors">
                Add Emergency Train
              </button>
              <button className="w-full bg-green-600 hover:bg-green-700 rounded-lg py-2 px-3 text-sm font-semibold transition-colors">
                Optimize Current State
              </button>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Clock className="w-5 h-5 text-yellow-400" />
              Recent Events
            </h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {events.slice(0, 10).map((event, index) => (
                <div key={index} className="bg-gray-900 rounded p-2">
                  <div className="text-xs text-gray-500">
                    {new Date().toLocaleTimeString()}
                  </div>
                  <div className="text-sm text-gray-300">
                    {event.message}
                  </div>
                </div>
              ))}
              {events.length === 0 && (
                <div className="text-gray-500 text-sm">
                  No events yet. Start the simulation to see live events.
                </div>
              )}
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-400" />
              Quick Actions
            </h3>
            <div className="space-y-2">
              <button className="w-full bg-gray-700 hover:bg-gray-600 rounded py-2 px-3 text-sm transition-colors">
                Emergency Stop All
              </button>
              <button className="w-full bg-gray-700 hover:bg-gray-600 rounded py-2 px-3 text-sm transition-colors">
                Clear All Delays
              </button>
              <button className="w-full bg-gray-700 hover:bg-gray-600 rounded py-2 px-3 text-sm transition-colors">
                Reset Simulation
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Stats Bar */}
      <div className="mt-6 bg-gray-800 rounded-lg p-4">
        <div className="grid grid-cols-6 gap-4">
          {demoStats && (
            <>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">{demoStats.network_size.stations}</div>
                <div className="text-sm text-gray-400">Stations</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">{demoStats.performance.on_time_performance.current}</div>
                <div className="text-sm text-gray-400">On-Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">{demoStats.performance.average_delay.current}</div>
                <div className="text-sm text-gray-400">Avg Delay</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">{demoStats.ai_metrics.decisions_per_minute}</div>
                <div className="text-sm text-gray-400">Decisions/min</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-400">{demoStats.ai_metrics.accuracy}</div>
                <div className="text-sm text-gray-400">AI Accuracy</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-cyan-400">{demoStats.ai_metrics.response_time}</div>
                <div className="text-sm text-gray-400">Response Time</div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;