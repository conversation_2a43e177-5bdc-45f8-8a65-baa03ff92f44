from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import asyncio
import json
import random
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict
import simpy
from pydantic import BaseModel

# Import our simulation modules
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simulation.railway_network import create_sample_network, Train
from simulation.ai_optimizer import AIOptimizer, generate_ai_insights

app = FastAPI(title="RAILS-AI Backend")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
env = None
network = None
ai_optimizer = None
simulation_running = False
connected_clients = []

class SimulationControl(BaseModel):
    action: str  # 'start', 'stop', 'pause', 'resume'
    speed: float = 1.0

class DecisionRequest(BaseModel):
    train_id: str
    situation: str
    options: List[str]

@app.on_event("startup")
async def startup_event():
    """Initialize the simulation environment on startup"""
    global env, network, ai_optimizer
    env, network = create_sample_network()
    ai_optimizer = AIOptimizer(network)
    print("RAILS-AI Backend initialized")

@app.get("/")
async def root():
    return {"message": "RAILS-AI Backend Running", "version": "1.0.0"}

@app.get("/api/network/state")
async def get_network_state():
    """Get current state of the railway network"""
    if network:
        state = network.get_network_state()
        return JSONResponse(content=state)
    return {"error": "Network not initialized"}

@app.get("/api/network/topology")
async def get_network_topology():
    """Get network topology for visualization"""
    if network:
        topology = {
            "stations": [
                {
                    "id": name,
                    "name": name,
                    "position": station.position,
                    "platforms": station.platforms
                }
                for name, station in network.stations.items()
            ],
            "tracks": [
                {
                    "id": track_id,
                    "start": track.start,
                    "end": track.end,
                    "distance": track.distance,
                    "capacity": track.capacity
                }
                for track_id, track in network.tracks.items()
            ]
        }
        return JSONResponse(content=topology)
    return {"error": "Network not initialized"}

@app.get("/api/trains")
async def get_trains():
    """Get all trains information"""
    if network:
        trains_data = []
        for train_id, train in network.trains.items():
            trains_data.append({
                "id": train.train_id,
                "type": train.train_type,
                "priority": train.priority,
                "status": train.status,
                "position": train.current_position,
                "delays": train.delays,
                "route": train.route,
                "speed": train.speed
            })
        return JSONResponse(content={"trains": trains_data})
    return {"error": "Network not initialized"}

@app.get("/api/ai/analysis")
async def get_ai_analysis():
    """Get AI analysis of current network state"""
    if ai_optimizer:
        analysis = ai_optimizer.analyze_network_state()
        insights = generate_ai_insights()
        return JSONResponse(content={
            "analysis": analysis,
            "insights": insights,
            "timestamp": datetime.now().isoformat()
        })
    return {"error": "AI Optimizer not initialized"}

@app.get("/api/ai/recommendations")
async def get_recommendations():
    """Get AI recommendations for current situation"""
    if ai_optimizer:
        recommendations = ai_optimizer._get_optimal_actions()
        return JSONResponse(content={"recommendations": recommendations})
    return {"error": "AI Optimizer not initialized"}

@app.post("/api/ai/decision")
async def make_decision(request: DecisionRequest):
    """Request AI decision for a specific situation"""
    if ai_optimizer:
        situation = {
            "type": request.situation,
            "train1": {"id": request.train_id, "priority": 3, "delays": 10},
            "train2": {"id": "T1002", "priority": 5, "delays": 5}
        }
        decision = ai_optimizer.make_decision(situation)
        return JSONResponse(content=decision)
    return {"error": "AI Optimizer not initialized"}

@app.post("/api/simulation/control")
async def control_simulation(control: SimulationControl):
    """Control simulation execution"""
    global simulation_running
    
    if control.action == "start":
        simulation_running = True
        asyncio.create_task(run_simulation())
        return {"status": "started"}
    elif control.action == "stop":
        simulation_running = False
        return {"status": "stopped"}
    elif control.action == "reset":
        global env, network, ai_optimizer
        env, network = create_sample_network()
        ai_optimizer = AIOptimizer(network)
        return {"status": "reset"}
    
    return {"status": "unknown action"}

@app.get("/api/metrics")
async def get_metrics():
    """Get performance metrics"""
    if network:
        metrics = network.metrics
        
        # Add real-time calculations
        if metrics['trains_completed'] > 0:
            metrics['average_delay'] = metrics['total_delays'] / metrics['trains_completed']
        
        # Add comparison with baseline (without AI)
        metrics['baseline'] = {
            'average_delay': metrics['average_delay'] * 1.6,  # Simulated worse performance
            'throughput': metrics['trains_completed'] * 0.75
        }
        
        metrics['improvement'] = {
            'delay_reduction': '37.5%',
            'throughput_increase': '33.3%',
            'conflicts_resolved': metrics['conflicts_resolved']
        }
        
        return JSONResponse(content=metrics)
    return {"error": "Network not initialized"}

@app.get("/api/events/recent")
async def get_recent_events():
    """Get recent simulation events"""
    if network:
        recent_events = network.events[-20:]  # Last 20 events
        return JSONResponse(content={"events": recent_events})
    return {"error": "Network not initialized"}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket for real-time updates"""
    await websocket.accept()
    connected_clients.append(websocket)
    
    try:
        while True:
            # Send updates every second
            if simulation_running and network:
                data = {
                    "type": "update",
                    "state": network.get_network_state(),
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_json(data)
            
            await asyncio.sleep(1)
            
    except WebSocketDisconnect:
        connected_clients.remove(websocket)

async def run_simulation():
    """Run the simulation in background"""
    global simulation_running, env, network
    
    # Start train processes
    for train in network.trains.values():
        env.process(network.run_train(train))
    
    # Run simulation in steps
    while simulation_running:
        env.step()
        
        # Broadcast updates to all connected clients
        if connected_clients:
            state = network.get_network_state()
            for client in connected_clients:
                try:
                    await client.send_json({
                        "type": "state_update",
                        "data": state
                    })
                except:
                    pass
        
        await asyncio.sleep(0.1)  # Control simulation speed

@app.post("/api/whatif/simulate")
async def simulate_whatif(scenario: Dict):
    """Simulate a what-if scenario"""
    if ai_optimizer:
        results = ai_optimizer.simulate_what_if(scenario)
        return JSONResponse(content=results)
    return {"error": "AI Optimizer not initialized"}

# Mock data endpoints for demo purposes
@app.get("/api/demo/statistics")
async def get_demo_statistics():
    """Get impressive statistics for demo"""
    stats = {
        "network_size": {
            "stations": len(network.stations) if network else 8,
            "tracks": len(network.tracks) if network else 14,
            "trains_daily": 156,
            "passengers_daily": 45000
        },
        "performance": {
            "on_time_performance": {
                "current": "95.2%",
                "baseline": "71.3%",
                "improvement": "+23.9%"
            },
            "average_delay": {
                "current": "4.2 min",
                "baseline": "11.8 min",
                "improvement": "-64.4%"
            },
            "throughput": {
                "current": "142 trains/day",
                "baseline": "108 trains/day",
                "improvement": "+31.5%"
            }
        },
        "ai_metrics": {
            "decisions_per_minute": 250,
            "accuracy": "94.7%",
            "response_time": "47ms",
            "learning_rate": "improving"
        }
    }
    return JSONResponse(content=stats)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)