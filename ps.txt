alright so i have a problem statement and i need its solution, i want the solution to be innovative and very efficient than current tools, we need to create a prototype for this problem statement, i dont even understand what we are making and how we are doing it, research properly lookup everything, current system and stuff and just make me a compelling solution prototype which will surely win. you dont have to code, but you need to explain whole prototype architecture, like everything properly i just need an efficient working prototype which i can present for this hackathon, explain what im supposed to build step by step everything dont leave anything, here is the problem statement i have attached it, just do what you need to do think properly, and dont create a mess