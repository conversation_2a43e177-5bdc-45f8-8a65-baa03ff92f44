import simpy
import random
import json
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import networkx as nx

class Station:
    def __init__(self, name, platforms=2, position=(0,0)):
        self.name = name
        self.platforms = platforms
        self.position = position
        self.occupied_platforms = []
        self.waiting_trains = []
        
class Track:
    def __init__(self, start, end, distance, capacity=1):
        self.start = start
        self.end = end
        self.distance = distance
        self.capacity = capacity
        self.current_trains = []
        
class Train:
    def __init__(self, train_id, train_type, route, priority, speed=60):
        self.train_id = train_id
        self.train_type = train_type  # 'express', 'local', 'freight'
        self.route = route  # List of stations
        self.priority = priority  # 1-5, 5 being highest
        self.speed = speed  # km/h
        self.current_position = route[0] if route else None
        self.next_station_index = 1
        self.status = 'scheduled'
        self.delays = 0
        self.journey_log = []
        
class RailwayNetwork:
    def __init__(self, env):
        self.env = env
        self.stations = {}
        self.tracks = {}
        self.trains = {}
        self.graph = nx.Graph()
        self.events = []
        self.metrics = {
            'total_delays': 0,
            'trains_completed': 0,
            'conflicts_resolved': 0,
            'average_delay': 0
        }
        
    def add_station(self, name, platforms=2, position=(0,0)):
        self.stations[name] = Station(name, platforms, position)
        self.graph.add_node(name, pos=position)
        
    def add_track(self, start, end, distance, capacity=1):
        track_id = f"{start}-{end}"
        self.tracks[track_id] = Track(start, end, distance, capacity)
        self.graph.add_edge(start, end, weight=distance)
        
    def add_train(self, train):
        self.trains[train.train_id] = train
        
    def find_path(self, start, end):
        try:
            return nx.shortest_path(self.graph, start, end, weight='weight')
        except nx.NetworkXNoPath:
            return None
            
    def check_track_availability(self, start, end):
        track_id = f"{start}-{end}"
        if track_id in self.tracks:
            track = self.tracks[track_id]
            return len(track.current_trains) < track.capacity
        return False
        
    def run_train(self, train):
        """Simulates a train journey through its route"""
        train.status = 'running'
        train.journey_log.append({
            'time': self.env.now,
            'event': 'departed',
            'station': train.current_position
        })
        
        for i in range(train.next_station_index, len(train.route)):
            current = train.route[i-1]
            next_station = train.route[i]
            
            # Check track availability
            while not self.check_track_availability(current, next_station):
                # Wait if track is occupied
                train.status = 'waiting'
                train.delays += 1
                self.log_event(f"Train {train.train_id} waiting at {current} for track clearance")
                yield self.env.timeout(5)  # Wait 5 minutes
                
            # Occupy track
            track_id = f"{current}-{next_station}"
            if track_id in self.tracks:
                self.tracks[track_id].current_trains.append(train.train_id)
                
            # Calculate travel time
            distance = self.tracks[track_id].distance if track_id in self.tracks else 50
            travel_time = (distance / train.speed) * 60  # Convert to minutes
            
            # Add random delays (weather, signals, etc.)
            if random.random() < 0.2:  # 20% chance of minor delay
                delay = random.randint(2, 10)
                travel_time += delay
                train.delays += delay
                self.log_event(f"Train {train.train_id} delayed by {delay} minutes due to signal")
                
            train.status = 'running'
            self.log_event(f"Train {train.train_id} traveling from {current} to {next_station}")
            
            # Travel
            yield self.env.timeout(travel_time)
            
            # Clear track
            if track_id in self.tracks:
                self.tracks[track_id].current_trains.remove(train.train_id)
                
            # Arrive at station
            train.current_position = next_station
            train.journey_log.append({
                'time': self.env.now,
                'event': 'arrived',
                'station': next_station
            })
            
            # Platform dwell time
            dwell_time = 2 if train.train_type == 'express' else 5
            yield self.env.timeout(dwell_time)
            
        train.status = 'completed'
        self.metrics['trains_completed'] += 1
        self.metrics['total_delays'] += train.delays
        self.log_event(f"Train {train.train_id} completed journey with {train.delays} minutes delay")
        
    def log_event(self, message):
        self.events.append({
            'time': self.env.now,
            'message': message
        })
        
    def get_network_state(self):
        """Returns current state of the network for AI processing"""
        state = {
            'time': self.env.now,
            'trains': {},
            'tracks': {},
            'stations': {},
            'metrics': self.metrics
        }
        
        for train_id, train in self.trains.items():
            state['trains'][train_id] = {
                'position': train.current_position,
                'status': train.status,
                'delays': train.delays,
                'priority': train.priority,
                'type': train.train_type
            }
            
        for track_id, track in self.tracks.items():
            state['tracks'][track_id] = {
                'occupied': len(track.current_trains) > 0,
                'trains': track.current_trains,
                'capacity_used': len(track.current_trains) / track.capacity
            }
            
        return state

def create_sample_network():
    """Creates a sample railway network for demo"""
    env = simpy.Environment()
    network = RailwayNetwork(env)
    
    # Add major stations (simplified Indian railway corridor)
    stations = [
        ('New Delhi', (28.6139, 77.2090)),
        ('Ghaziabad', (28.6692, 77.4538)),
        ('Aligarh', (27.8974, 78.0880)),
        ('Kanpur', (26.4499, 80.3319)),
        ('Allahabad', (25.4358, 81.8463)),
        ('Mughal Sarai', (25.2838, 83.0101)),
        ('Patna', (25.5941, 85.1376)),
        ('Howrah', (22.5726, 88.3639))
    ]
    
    for name, pos in stations:
        network.add_station(name, platforms=random.randint(2, 6), position=pos)
    
    # Add tracks between consecutive stations
    for i in range(len(stations) - 1):
        start = stations[i][0]
        end = stations[i+1][0]
        distance = random.randint(50, 200)  # km
        network.add_track(start, end, distance, capacity=2)
        network.add_track(end, start, distance, capacity=2)  # Bidirectional
    
    # Add some trains
    train_types = ['express', 'express', 'local', 'freight', 'express', 'local']
    for i in range(6):
        route = [s[0] for s in stations[::2 if i % 2 == 0 else 1]]  # Different routes
        train = Train(
            train_id=f"T{1001+i}",
            train_type=train_types[i],
            route=route,
            priority=5 if train_types[i] == 'express' else 3,
            speed=100 if train_types[i] == 'express' else 60
        )
        network.add_train(train)
    
    return env, network

# Example usage for testing
if __name__ == "__main__":
    env, network = create_sample_network()
    
    # Start train processes
    for train in network.trains.values():
        env.process(network.run_train(train))
    
    # Run simulation for 500 time units (minutes)
    env.run(until=500)
    
    # Print results
    print(f"Simulation completed at time {env.now}")
    print(f"Trains completed: {network.metrics['trains_completed']}")
    print(f"Total delays: {network.metrics['total_delays']} minutes")
    print(f"Events logged: {len(network.events)}")