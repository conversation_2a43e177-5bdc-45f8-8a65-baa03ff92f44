import numpy as np
import random
from typing import Dict, List, Tuple
import gym
from gym import spaces
import torch
import torch.nn as nn
import torch.nn.functional as F
from collections import deque
import networkx as nx

class RailwayEnvironment(gym.Env):
    """Custom Gym environment for railway optimization"""
    
    def __init__(self, network):
        super(RailwayEnvironment, self).__init__()
        self.network = network
        
        # Define action space: hold, release, change platform, change speed
        self.action_space = spaces.Discrete(4)
        
        # Define observation space (simplified)
        # [num_trains_waiting, avg_delay, track_occupancy, time_of_day]
        self.observation_space = spaces.Box(
            low=np.array([0, 0, 0, 0]),
            high=np.array([100, 1000, 1, 24]),
            dtype=np.float32
        )
        
        self.current_step = 0
        self.max_steps = 1000
        
    def reset(self):
        self.current_step = 0
        # Return initial observation
        return self._get_observation()
        
    def step(self, action):
        # Execute action
        self._take_action(action)
        
        # Calculate reward
        reward = self._calculate_reward()
        
        # Check if episode is done
        self.current_step += 1
        done = self.current_step >= self.max_steps
        
        # Get new observation
        obs = self._get_observation()
        
        return obs, reward, done, {}
        
    def _get_observation(self):
        state = self.network.get_network_state()
        
        # Extract features
        num_waiting = sum(1 for t in state['trains'].values() if t['status'] == 'waiting')
        avg_delay = np.mean([t['delays'] for t in state['trains'].values()]) if state['trains'] else 0
        
        occupied_tracks = sum(1 for t in state['tracks'].values() if t['occupied'])
        total_tracks = len(state['tracks']) if state['tracks'] else 1
        track_occupancy = occupied_tracks / total_tracks
        
        time_of_day = (self.network.env.now % 1440) / 60  # Convert to hours
        
        return np.array([num_waiting, avg_delay, track_occupancy, time_of_day], dtype=np.float32)
        
    def _take_action(self, action):
        # Simplified action execution
        if action == 0:  # Hold trains
            pass
        elif action == 1:  # Release trains
            pass
        elif action == 2:  # Change platform
            pass
        elif action == 3:  # Adjust speed
            pass
            
    def _calculate_reward(self):
        state = self.network.get_network_state()
        
        # Reward function
        delays = sum(t['delays'] for t in state['trains'].values())
        completed = state['metrics']['trains_completed']
        
        # Negative reward for delays, positive for completions
        reward = completed * 100 - delays * 2
        
        return reward

class GraphNeuralNetwork(nn.Module):
    """Simplified GNN for processing railway network state"""
    
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(GraphNeuralNetwork, self).__init__()
        self.conv1 = nn.Linear(input_dim, hidden_dim)
        self.conv2 = nn.Linear(hidden_dim, hidden_dim)
        self.conv3 = nn.Linear(hidden_dim, output_dim)
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x, edge_index=None):
        x = F.relu(self.conv1(x))
        x = self.dropout(x)
        x = F.relu(self.conv2(x))
        x = self.dropout(x)
        x = self.conv3(x)
        return x

class DQNAgent:
    """Deep Q-Network Agent for decision making"""
    
    def __init__(self, state_size, action_size, learning_rate=0.001):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.epsilon = 1.0  # Exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = learning_rate
        
        # Neural network for Q-learning
        self.q_network = self._build_model()
        self.target_network = self._build_model()
        
    def _build_model(self):
        model = nn.Sequential(
            nn.Linear(self.state_size, 128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.ReLU(),
            nn.Linear(128, self.action_size)
        )
        return model
        
    def remember(self, state, action, reward, next_state, done):
        self.memory.append((state, action, reward, next_state, done))
        
    def act(self, state):
        if random.random() <= self.epsilon:
            return random.randrange(self.action_size)
        
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        q_values = self.q_network(state_tensor)
        return np.argmax(q_values.detach().numpy())
        
    def replay(self, batch_size=32):
        if len(self.memory) < batch_size:
            return
            
        batch = random.sample(self.memory, batch_size)
        
        for state, action, reward, next_state, done in batch:
            target = reward
            if not done:
                next_state_tensor = torch.FloatTensor(next_state).unsqueeze(0)
                target = reward + 0.95 * torch.max(self.target_network(next_state_tensor)).item()
                
            state_tensor = torch.FloatTensor(state).unsqueeze(0)
            target_f = self.q_network(state_tensor)
            target_f[0][action] = target
            
            # Train the network
            # Simplified training step
            
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

class AIOptimizer:
    """Main AI Optimization System"""
    
    def __init__(self, network):
        self.network = network
        self.env = RailwayEnvironment(network)
        self.agent = DQNAgent(state_size=4, action_size=4)
        self.gnn = GraphNeuralNetwork(input_dim=10, hidden_dim=64, output_dim=32)
        self.decision_history = []
        
    def analyze_network_state(self):
        """Analyzes current network state and returns insights"""
        state = self.network.get_network_state()
        
        analysis = {
            'congestion_level': self._calculate_congestion(),
            'delay_prediction': self._predict_delays(),
            'optimal_actions': self._get_optimal_actions(),
            'risk_assessment': self._assess_risks()
        }
        
        return analysis
        
    def _calculate_congestion(self):
        state = self.network.get_network_state()
        occupied = sum(1 for t in state['tracks'].values() if t['occupied'])
        total = len(state['tracks']) if state['tracks'] else 1
        
        congestion = occupied / total
        
        if congestion < 0.3:
            return {'level': 'low', 'value': congestion, 'color': 'green'}
        elif congestion < 0.7:
            return {'level': 'medium', 'value': congestion, 'color': 'yellow'}
        else:
            return {'level': 'high', 'value': congestion, 'color': 'red'}
            
    def _predict_delays(self):
        """Predicts future delays based on current state"""
        state = self.network.get_network_state()
        
        # Simple prediction model
        current_delays = [t['delays'] for t in state['trains'].values()]
        avg_delay = np.mean(current_delays) if current_delays else 0
        
        # Predict delays will increase if congestion is high
        congestion = self._calculate_congestion()['value']
        predicted_delay = avg_delay * (1 + congestion * 0.5)
        
        return {
            'current_avg': avg_delay,
            'predicted_avg': predicted_delay,
            'trend': 'increasing' if predicted_delay > avg_delay else 'stable'
        }
        
    def _get_optimal_actions(self):
        """Returns list of recommended actions"""
        obs = self.env._get_observation()
        action_idx = self.agent.act(obs)
        
        actions = ['Hold trains', 'Release trains', 'Change platform', 'Adjust speed']
        
        recommendations = []
        
        # Analyze specific situations
        state = self.network.get_network_state()
        
        for train_id, train in state['trains'].items():
            if train['status'] == 'waiting' and train['delays'] > 10:
                recommendations.append({
                    'train': train_id,
                    'action': 'Prioritize',
                    'reason': f"Train has {train['delays']} min delay",
                    'priority': 'high'
                })
                
        # Add general recommendation
        recommendations.append({
            'train': 'all',
            'action': actions[action_idx],
            'reason': 'AI optimization suggestion',
            'priority': 'medium'
        })
        
        return recommendations
        
    def _assess_risks(self):
        """Assesses potential risks in the network"""
        risks = []
        state = self.network.get_network_state()
        
        # Check for bottlenecks
        for track_id, track in state['tracks'].items():
            if track['capacity_used'] > 0.8:
                risks.append({
                    'type': 'bottleneck',
                    'location': track_id,
                    'severity': 'high',
                    'message': f"Track {track_id} near capacity"
                })
                
        # Check for cascading delays
        high_delay_trains = [t for t in state['trains'].values() if t['delays'] > 20]
        if len(high_delay_trains) > 2:
            risks.append({
                'type': 'cascade',
                'severity': 'medium',
                'message': f"{len(high_delay_trains)} trains with significant delays"
            })
            
        return risks
        
    def make_decision(self, situation):
        """Makes a specific decision for a given situation"""
        decision = {
            'timestamp': self.network.env.now,
            'situation': situation,
            'recommendation': None,
            'confidence': 0.0,
            'explanation': ''
        }
        
        # Example decision logic
        if situation['type'] == 'crossing':
            # Decide which train gets priority at crossing
            train1 = situation['train1']
            train2 = situation['train2']
            
            # Priority-based decision
            if train1['priority'] > train2['priority']:
                decision['recommendation'] = f"Give priority to {train1['id']}"
                decision['confidence'] = 0.9
                decision['explanation'] = f"Higher priority train (P{train1['priority']} vs P{train2['priority']})"
            elif train1['delays'] > train2['delays']:
                decision['recommendation'] = f"Give priority to {train1['id']}"
                decision['confidence'] = 0.8
                decision['explanation'] = f"Train has higher delays ({train1['delays']} min)"
            else:
                decision['recommendation'] = f"Give priority to {train2['id']}"
                decision['confidence'] = 0.7
                decision['explanation'] = "Based on overall network optimization"
                
        self.decision_history.append(decision)
        return decision
        
    def simulate_what_if(self, scenario):
        """Simulates a what-if scenario"""
        # Create a copy of current state
        # Apply scenario changes
        # Run simulation
        # Return predicted outcomes
        
        results = {
            'scenario': scenario,
            'predicted_delays': random.randint(5, 30),
            'throughput_change': random.uniform(-10, 20),
            'recommendation': 'Proceed with caution'
        }
        
        return results

# Utility functions for demonstration
def generate_ai_insights():
    """Generates sample AI insights for demo"""
    insights = {
        'optimization_potential': '35%',
        'predicted_improvement': {
            'delays': '-40%',
            'throughput': '+25%',
            'energy': '-15%'
        },
        'key_recommendations': [
            'Implement dynamic speed advisory for Express trains',
            'Adjust platform allocation at Kanpur station',
            'Prioritize freight trains during off-peak hours'
        ],
        'confidence_score': 0.87
    }
    return insights